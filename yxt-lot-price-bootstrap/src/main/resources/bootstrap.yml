apollo:
  meta: http://10.4.3.218:9100
  bootstrap:
    enabled: true #本地不需从apollo拉取配置
    eagerLoad:
      enabled: false
    namespaces: application,bizconfig,thirdconfig
spring:
  profiles:
    active: local
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
grey:
  enable: true
  local-mappings:
    '[(.+)]': $1.svc.k8s.dev.hxyxt.com
#grey.local-mappings.'yxt-bigdata-bee': bigdata-bee.svc.k8s.dev.hxyxt.com


# 禁用所有 RocketMQ 监听器
rocketmq:
  consumer:
    enabled: false  # 全局关闭所有消费者



