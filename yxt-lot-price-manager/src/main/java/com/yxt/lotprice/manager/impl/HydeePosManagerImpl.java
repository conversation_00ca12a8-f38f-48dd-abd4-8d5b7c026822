package com.yxt.lotprice.manager.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yxt.common.wechatrobot.util.WxRobotOkHttpUtils;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.lang.util.ExLogger;
import com.yxt.lotprice.common.config.PriceRobotConfig;
import com.yxt.lotprice.common.util.LocalDateUtils;
import com.yxt.lotprice.manager.config.HdPosProperties;
import com.yxt.lotprice.service.manager.iface.HydeePosManager;
import com.yxt.lotprice.service.model.dto.sync.hdpos.HdPosReqDTO;
import com.yxt.lotprice.service.model.dto.sync.hdpos.HdPosRespBaseDTO;
import com.yxt.lotprice.service.model.enums.third.HdApiEnum;
import com.yxt.lotprice.service.utils.HdSignUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import org.springframework.web.client.RestTemplate;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

/**
 * Since: 2025/06/13 16:14
 * Author: qs
 */

@Slf4j
@Service
public class HydeePosManagerImpl implements HydeePosManager {

    @Resource
    private HdPosProperties hdPosProperties;

    @Resource
    @Qualifier("hdRestTemplate")
    private RestTemplate restTemplate;

    @Override
    public <T> HdPosRespBaseDTO<T> execute(HdApiEnum hdApiEnum, String companyCode, String storeCode, String bizContent, Class<T> responseDataClass) {
        // 1. 参数校验
        validateConfig(hdApiEnum, companyCode, storeCode, bizContent);

        // 2. 模拟请求（Mock模式）
        if (hdPosProperties.getMockSend()) {
            log.info("[Mock] 模拟请求POS接口: {}, bizContent: {}", hdApiEnum.getApi(), bizContent);
            // 返回模拟成功响应
            return HdPosRespBaseDTO.<T>success();
        }

        // 3. 构建请求DTO
        HdPosReqDTO reqDTO = buildRequestDTO(bizContent);

        // 4. 签名
        signRequest(reqDTO);

        // 5. 构建HTTP请求
        HttpEntity<HdPosReqDTO> requestEntity = buildHttpRequest(reqDTO, companyCode, storeCode);

        // 6. 发送请求（支持重试）
        return sendRequestWithRetry(hdApiEnum, requestEntity);
    }

    /**
     * 校验必要参数
     */
    private void validateConfig(HdApiEnum hdApiEnum, String companyCode, String storeCode, String bizContent) {
        if (StringUtils.isEmpty(hdPosProperties.getPosUrl())) {
            throw new YxtBizException("未配置海典POS请求地址");
        }
        if (StringUtils.isEmpty(companyCode) || StringUtils.isEmpty(storeCode)) {
            throw new YxtBizException("公司编码或门店编码不能为空");
        }
        if (hdApiEnum == null) {
            throw new YxtBizException("API枚举不能为空");
        }
    }

    /**
     * 构建请求DTO
     */
    private HdPosReqDTO buildRequestDTO(String bizContent) {
        HdPosReqDTO reqDTO = new HdPosReqDTO();
        reqDTO.setBiz_content(bizContent);
        reqDTO.setApp_id(hdPosProperties.getAppId());
        reqDTO.setCharset(StandardCharsets.UTF_8.name());
        reqDTO.setFormat("json");
        reqDTO.setSign_type("RSA2");
        reqDTO.setVersion("1.0");
        reqDTO.setTimestamp(LocalDateUtils.localDateTime2String(LocalDateTime.now()));
        return reqDTO;
    }

    /**
     * 对请求参数签名
     */
    private void signRequest(HdPosReqDTO reqDTO) {
        try {
            Map<String, String> paramsMap = JSON.parseObject(JSON.toJSONString(reqDTO), new TypeReference<Map<String, String>>() {});
            String sign = HdSignUtil.sign(paramsMap, hdPosProperties.getPrivateKey());
            reqDTO.setSign(sign);
        } catch (Exception e) {
            ExLogger.logger().warn("调用海典签名异常:", e);
            throw new YxtBizException("调用海典签名异常");
        }
    }

    /**
     * 构建HTTP请求实体
     */
    private HttpEntity<HdPosReqDTO> buildHttpRequest(HdPosReqDTO reqDTO, String companyCode, String storeCode) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("request_id", "YXT" + ThreadLocalRandom.current().nextLong(1000000000000000L, 10000000000000000L));
        headers.add("mdm_busno", storeCode);
        headers.add("compid", companyCode);
        headers.add("timestamp", reqDTO.getTimestamp());
        return new HttpEntity<>(reqDTO, headers);
    }

    /**
     * 带重试机制的请求发送
     */
    private <T> HdPosRespBaseDTO<T> sendRequestWithRetry(HdApiEnum hdApiEnum, HttpEntity<HdPosReqDTO> requestEntity) {
        Exception lastException = null;
        int retryCount = hdPosProperties.getRetryCount();

        for (int attempt = 1; attempt <= retryCount; attempt++) {
            try {
                log.info("开始第 {} 次请求海典POS接口: {}", attempt, hdApiEnum.getApi());

                StopWatch stopWatch = new StopWatch();
                stopWatch.start();
                // 发送请求（支持泛型解析）
                ResponseEntity<HdPosRespBaseDTO<T>> response = restTemplate.exchange(
                        hdPosProperties.getPosUrl() + hdApiEnum.getApi(),
                        HttpMethod.POST,
                        requestEntity,
                        new ParameterizedTypeReference<HdPosRespBaseDTO<T>>() {}
                );

                // 检查HTTP状态码
                if (!response.getStatusCode().is2xxSuccessful()) {
                    stopWatch.stop();
                    long totalTimeMillis = stopWatch.getTotalTimeMillis();
                    log.debug("请求海典 POS HTTP状态码异常，API:{}, 耗时{}ms,请求：{}，响应: {}", hdApiEnum.getApi(), totalTimeMillis, requestEntity, JSON.toJSONString(response));
                    if (totalTimeMillis > 3000) {
                        // 告警
                        WxRobotOkHttpUtils.post(PriceRobotConfig.PRICE_ALERT, String.format("调用海典POS耗时过长，api：%s，耗时：%d，请求：%s", hdApiEnum.getApi(), totalTimeMillis, requestEntity.toString()));
                    }
                    throw new YxtBizException("HTTP状态码异常 httpStatusCode: " + response.getStatusCode());
                }

                // 检查响应体
                HdPosRespBaseDTO<T> responseBody = response.getBody();
                if (responseBody == null) {
                    throw new YxtBizException("海典返回空响应体");
                }

                stopWatch.stop();
                // 成功时返回数据
                log.info("请求海典POS成功,API: {}, 耗时{}ms，请求：{} 响应: {}", hdApiEnum.getApi(), stopWatch.getTotalTimeMillis(), requestEntity, JSON.toJSONString(responseBody));
                return responseBody;

            } catch (Exception e) {
                lastException = e;
                log.warn("第 {} 次请求海典POS失败，API:{}, 请求：{}", attempt, hdApiEnum.getApi(), requestEntity, e);

                // 非最后一次尝试时等待
                if (attempt < retryCount) {
                    try {
                        Thread.sleep(1000L * attempt); // 递增等待
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new YxtBizException("请求被中断");
                    }
                }
            }
        }

        // 所有重试均失败
        String errorMsg = String.format("请求海典POS失败，重试 %d 次后仍不可用", retryCount);
        log.error(errorMsg, lastException);
        throw new YxtBizException(errorMsg);
    }

}
