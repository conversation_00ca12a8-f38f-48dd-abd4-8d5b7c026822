package com.yxt.lotprice.application.mq.rocketmq.consumer;

import com.alibaba.fastjson.JSON;
import com.yxt.lang.util.ExLogger;
import com.yxt.lotprice.common.model.constant.MqConsumerGroupConstant;
import com.yxt.lotprice.common.model.constant.MqTopicConstant;
import com.yxt.lotprice.service.model.dto.mq.rocketmq.StorePriceDTO;
import com.yxt.lotprice.service.sync.PriceSyncService;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;

/**
 * Since: 2025/06/13 10:55
 * Author: qs
 */

@Component
@RocketMQMessageListener(
        topic = MqTopicConstant.TP_PUBLISH_PRICE,
        consumerGroup = MqConsumerGroupConstant.CG_PRICE_SYNC_POS, consumeThreadMax = 5)
public class PriceSyncPosConsumer implements RocketMQListener<StorePriceDTO> {

    @Resource
    private PriceSyncService priceSyncService;

    @Override
    public void onMessage(StorePriceDTO storePriceDTO) {
        if (storePriceDTO == null) {
            ExLogger.logger().info("PriceSyncPosConsumer收到消息为空");
            return;
        }
        ExLogger.logger().info("PriceSyncPosConsumer收到消息：{}", JSON.toJSONString(storePriceDTO));
        priceSyncService.priceSync(storePriceDTO);
    }

}
