### 按照调价时间重新计算价格并下发
POST http://localhost:8080/b/hand/calculationPriceByFormTime?beginTime=2025-06-24 00:09:00&endTime=2025-06-24 23:09:16

### 按照调价单重新计算下发
POST http://yxt-lot-price.svc.k8s.test.hxyxt.com/b/hand/calculationPriceByFormNo
Content-Type: application/json

["TJ20250626000037"]


### 手动同步价格到海典
POST http://localhost:8080/b/hand/send2Pos?pricePublishSource=HAND
#POST http://yxt-lot-price.svc.k8s.test.hxyxt.com/b/hand/send2Pos?pricePublishSource=HAND
Content-Type: application/json


{
  "goodsList": [
    {
      "erpCode": "118929",
      "priceList": [
        {
          "endTime": "2025-06-25",
          "formNo": "TJ20250625000024",
          "price": "2.5000",
          "startTime": "2025-06-25"
        }
      ],
      "priceType": "RETAIL"
    }
  ],
  "storeCode": "H812",
  "timestamp": "1750832377317"
}

###
POST http://localhost:8080/b/hand/syncPriceToPosByStore
Content-Type: application/json

[
  "H812"
]

###
GET http://localhost:8080/b/hand/calculation?formId=TJ20250626000037&formItemId=1938181910408859648&taskId=430
Content-Type: application/json