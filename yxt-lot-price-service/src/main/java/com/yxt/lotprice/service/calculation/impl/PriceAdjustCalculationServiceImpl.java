package com.yxt.lotprice.service.calculation.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.yxt.common.wechatrobot.util.WxRobotOkHttpUtils;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.lang.util.ExLogger;
import com.yxt.lotprice.common.config.PriceRobotConfig;
import com.yxt.lotprice.common.sharding.AbstractShardingAlgorithm;
import com.yxt.lotprice.service.BatchTaskExtService;
import com.yxt.lotprice.service.StoreService;
import com.yxt.lotprice.service.cache.AllStoreInfoCache;
import com.yxt.lotprice.service.calculation.PriceAdjustCalculationService;
import com.yxt.lotprice.service.PriceAdjustFormItemService;
import com.yxt.lotprice.service.calculation.PriceAdjustFormItemDetailService;
import com.yxt.lotprice.service.calculation.PriceAdjustResultsService;
import com.yxt.lotprice.service.model.dto.export.CalculationPriceFailExcelDTO;
import com.yxt.lotprice.service.utils.RedisUtil;
import com.yxt.lotprice.service.constant.RedisConstant;
import com.yxt.common.file.service.OssFileService;
import com.alibaba.excel.EasyExcelFactory;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;

import com.yxt.lotprice.service.manager.sdk.dto.StoreDTO;
import com.yxt.lotprice.service.model.bo.*;
import com.yxt.lotprice.service.model.enums.*;
import com.yxt.lotprice.service.model.enums.log.PriceSourceEnum;
import com.yxt.lotprice.service.model.enums.log.PriceSourceEnum;
import com.yxt.lotprice.service.mq.PricePublishService;
import com.yxt.lotprice.service.utils.PriceCompareUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 价格调整计算Service实现类
 *
 * <AUTHOR>
 * @since 2025-05-21
 */

@Service
public class PriceAdjustCalculationServiceImpl implements PriceAdjustCalculationService {

    @Resource
    private PriceAdjustFormItemService priceAdjustFormItemService;
    @Resource
    private PriceAdjustFormItemDetailService priceAdjustFormItemDetailService;
    @Resource
    private PriceAdjustResultsService priceAdjustResultsService;
    @Resource
    private PricePublishService pricePublishService;
    @Resource
    private AllStoreInfoCache allStoreInfoCache;

    @Resource
    private OssFileService ossFileService;
    @Resource
    private StoreService storeService;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private BatchTaskExtService batchTaskExtService;

    @Override
    public void processAdjustPriceFormItem(String formNo, Long formItemId, Long taskId, PriceSourceEnum priceSourceEnum) {
        ExLogger.logger().info("开始处理调价单项目，ID: {}", formItemId);

        try {
            // 从price_adjust_form_item 表查询调价单项目数据 主库(包含执行明细)
            PriceAdjustFormItemBO formItemBO = priceAdjustFormItemService.getById(formNo, formItemId, true);
            if (formItemBO == null) {
                ExLogger.logger().error("调价单明细不存在，ID: {}", formItemId);
                WxRobotOkHttpUtils.post(PriceRobotConfig.PRICE_ALERT, String.format("调价单明细不存在id：%s", formItemId));
                taskHand(taskId);
                return;
            }
            if (!formItemBO.getIsValid().equals(1)) {
                priceAdjustFormItemService.formItemExecFinish(formNo, formItemId);
                taskHand(taskId);
                return;
            }
            // 集团 + 分公司要判断是否执行
            if (AdjustScopeType.GROUP.equals(formItemBO.getScope())
                    && DimensionEnum.COMPANY.equals(formItemBO.getOrgType())
                    && Integer.valueOf(0).equals(formItemBO.getExec())) {
                priceAdjustFormItemService.formItemExecFinish(formNo, formItemId);
                // 不执行
                taskHand(taskId);
                return;
            }

            // 保存调价单明细数据到price_adjust_form_item_detail表
            List<String> storeCodeList = priceAdjustFormItemDetailService.saveFormItemDetail(formItemBO, taskId);
            if (CollectionUtils.isEmpty(storeCodeList)) {
                priceAdjustFormItemService.formItemExecFinish(formNo, formItemId);
                taskHand(taskId);
                return;
            }

            // 计算门店价格
            List<PriceResultsBO> priceResultsBOList = new ArrayList<>(calculatePriceResult(formItemBO.getRequestPriceType(), storeCodeList, formItemBO.getErpCode()));

            // 保存计算的价格结果，获取下发数据
            List<PriceResultsBO> publishPriceList = savePriceResultAndGetPublishPrice(formItemBO.getErpCode(), formItemBO.getRequestPriceType(), priceResultsBOList, priceSourceEnum);

            pricePublishService.publishPrice(pricePublishService.buildPublishData(priceSourceEnum, publishPriceList));

            // 修改调价单明细状态
            priceAdjustFormItemService.formItemExecFinish(formNo, formItemId);
            // 处理任务
            taskHand(taskId);
            ExLogger.logger().info("处理调价单项目成功，ID: {}", formItemId);
        } catch (Exception e) {
            ExLogger.logger().error("处理调价单项目异常，ID: {}, 异常信息: {}", formItemId, e.getMessage(), e);
            throw e;
        }
    }

    private void taskHand(Long taskId) {
        if (taskId != null) {
            // todo 使用重试保证，异常不影响主流程
            String redisKey = String.format(RedisConstant.ADJUST_FORM_CALCULATION_PRICE_TASK_KEY_FORMAT, taskId);
            Long itemCount = redisUtil.decrement(String.format(RedisConstant.ADJUST_FORM_CALCULATION_PRICE_TASK_KEY_FORMAT, taskId));
            if (itemCount <= 0) {
                // 任务结束
                processTaskFailDataAndFinish(taskId);
                redisUtil.delete(redisKey);
            }
        }
    }

    @Override
    public void calculateSavePublish(PriceTypeEnum priceTypeEnum, String storeCode, List<String> erpCodeList, PriceSourceEnum sourceEnum) {
        List<PriceResultsBO> newList = calculatePriceResult(priceTypeEnum, null, storeCode, erpCodeList);
        List<PriceResultsBO> oldList = priceAdjustResultsService.listByStoreCodePriceTypeErpCodes(null, storeCode, priceTypeEnum, newList.stream().map(PriceResultsBO::getErpCode).collect(Collectors.toSet()), true);
        // 保存价格
        List<PriceResultsBO> publishList = saveOrUpdateAndGetPublishPrice(sourceEnum, newList, oldList);
        // 下发价格
        if (CollectionUtils.isNotEmpty(publishList)) {
            pricePublishService.publishPrice(pricePublishService.buildPublishData(sourceEnum, publishList));
        }
    }

    @Override
    public void storeChangeCalculateSavePublish(PriceTypeEnum priceTypeEnum, String oldCompanyCode, String newCompanyCode, String storeCode
            , List<String> erpCodeList, PriceSourceEnum sourceEnum) {
        StoreDTO storeDTO = allStoreInfoCache.getStore(storeCode);
        if (!(storeDTO != null && storeDTO.getStCode().equals(newCompanyCode))) {
            // 直接查询服务
            StoreDTO storeRemote = storeService.getByCode(storeCode);
            if (storeRemote == null) {
                throw new YxtBizException("门店信息为空");
            }
            if (StringUtils.isEmpty(storeRemote.getBranchCode())) {
                throw new YxtBizException("分公司编码为空");
            }
            newCompanyCode = storeRemote.getBranchCode();
        }

        List<PriceResultsBO> newList = calculatePriceResult(priceTypeEnum, newCompanyCode, storeCode, erpCodeList);
        List<PriceResultsBO> oldList = priceAdjustResultsService.listByStoreCodePriceTypeErpCodes(newCompanyCode, storeCode, priceTypeEnum, newList.stream().map(PriceResultsBO::getErpCode).collect(Collectors.toSet()), true);

        List<PriceResultsBO> toInsertList = new ArrayList<>();
        List<PriceResultsBO> toUpdateList = new ArrayList<>();

        Map<String, PriceResultsBO> oldMap = oldList.stream()
                .collect(Collectors.toMap(
                        PriceResultsBO::buildUniqueKey,
                        item -> item,
                        (v1, v2) -> v1
                ));

        // 原来分表的日志
        List<PriceResultsBO> oldLogList = priceAdjustResultsService.listLogByStoreCodePriceTypeErpCodes(oldCompanyCode, storeCode, priceTypeEnum, newList.stream().map(PriceResultsBO::getErpCode).collect(Collectors.toSet()));
        Map<String, PriceResultsBO> oldLogMap = oldLogList.stream()
                .collect(Collectors.toMap(
                        PriceResultsBO::buildUniqueKey,
                        item -> item,
                        (v1, v2) -> v1
                ));
        newList.forEach(newItem -> {
            PriceResultsBO old = oldMap.get(newItem.buildUniqueKey());
            PriceResultsBO oldLog = oldLogMap.get(newItem.buildUniqueKey());
            PriceResultsBO changeUpdateBO = BeanUtil.copyProperties(newItem, PriceResultsBO.class);

            // todo qs 新增一条日志
            if (oldLog != null) {
                changeUpdateBO.setPriceChangeLog(oldLog.getPriceChangeLog());
                changeUpdateBO.setPriceResultLog(oldLog.getPriceResultLog());
                changeUpdateBO.setPushPosLog(oldLog.getPushPosLog());
            }
            if (old == null) {
                toInsertList.add(changeUpdateBO);
            } else {
                toUpdateList.add(changeUpdateBO);
            }
        });

        Set<String> uniqueKeySet = new HashSet<>();
        ListUtils.partition(oldList, 20).forEach(partitionList -> {
            // 删除原数据，保存数据 使用事务保证老日志能迁移走
            transactionTemplate.execute(transactionStatus -> {
                // 删除数据
                priceAdjustResultsService.deleteByStoreAndIds(oldCompanyCode, storeCode, partitionList.stream().map(PriceResultsBO::getId).collect(Collectors.toSet()));
                // 新增/修改数据 筛选删除的数据做新增
                Set<String> tempUniqueKeySet = partitionList.stream().map(PriceResultsBO::buildUniqueKey).collect(Collectors.toSet());
                List<PriceResultsBO> tempInsertList = toInsertList.stream().filter(f -> tempUniqueKeySet.contains(f.getErpCode())).collect(Collectors.toList());
                List<PriceResultsBO> tempUpdateList = toUpdateList.stream().filter(f -> tempUniqueKeySet.contains(f.getErpCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(tempInsertList)) {
                    priceAdjustResultsService.saveBatch(tempInsertList, sourceEnum);
                }
                if (CollectionUtils.isNotEmpty(tempUpdateList)) {
                    priceAdjustResultsService.updateBatch(tempUpdateList, sourceEnum);
                }
                uniqueKeySet.addAll(tempUniqueKeySet);
                return null;
            });
        });

        // 新数据再保存一次
        List<PriceResultsBO> saveInsertList = toInsertList.stream().filter(f -> !uniqueKeySet.contains(f.getErpCode())).collect(Collectors.toList());
        List<PriceResultsBO> saveUpdateList = toUpdateList.stream().filter(f -> !uniqueKeySet.contains(f.getErpCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(saveInsertList)) {
            priceAdjustResultsService.saveBatch(saveInsertList, sourceEnum);
        }
        if (CollectionUtils.isNotEmpty(saveUpdateList)) {
            priceAdjustResultsService.updateBatch(saveUpdateList, sourceEnum);
        }

        // 下发价格，不做对比，都下发
        if (CollectionUtils.isNotEmpty(newList)) {
            pricePublishService.publishPrice(pricePublishService.buildPublishData(sourceEnum, newList));
        }
    }

    /**
     * 保存结果数据 新增/更新
     * 获取待下发数据
     *
     * @return 需要下发的数据
     */
    @Override
    public List<PriceResultsBO> savePriceResultAndGetPublishPrice(String erpCode, PriceTypeEnum priceType, List<PriceResultsBO> priceResultsBOList, PriceSourceEnum sourceEnum) {
        List<PriceResultsBO> publishPriceList = new ArrayList<>();
        Map<String, List<PriceResultsBO>> groupBySharding;
        if (priceResultsBOList.size() < 10000) {
            // 按照分表序号排序，每次少查表
            priceResultsBOList.sort(Comparator.comparingInt(
                    item -> priceAdjustResultsService.getTableHashIndex(item.getStoreCode())
            ));
            // 直接分批查询
            List<List<PriceResultsBO>> partition = ListUtils.partition(priceResultsBOList, 1000);
            groupBySharding = new HashMap<>();
            for (int i = 0; i < partition.size(); i++) {
                groupBySharding.put(String.valueOf(i), partition.get(i));
            }
        } else {
            // 按照分公司 门店hash 分组
            groupBySharding = priceResultsBOList.stream().collect(Collectors
                    .groupingBy(r -> priceAdjustResultsService.getTableName(r.getCompanyCode(), r.getStoreCode())));
        }
        groupBySharding.forEach((k, v) -> {
            String tableName = "";
            Set<String> companyCodeSet = null;
            if (k.contains(AbstractShardingAlgorithm.split)) {
                tableName = k;
            } else {
                companyCodeSet = v.stream().map(PriceResultsBO::getCompanyCode).collect(Collectors.toSet());
            }
            List<PriceResultsBO> priceResultsBODbList = priceAdjustResultsService.listByErpCodePriceTypeStoreCode(tableName, companyCodeSet
                    , v.stream().map(PriceResultsBO::getStoreCode).collect(Collectors.toSet()), erpCode, priceType, true);

            publishPriceList.addAll(saveOrUpdateAndGetPublishPrice(sourceEnum, v, priceResultsBODbList));
        });

        // 问题：数据库更新成功了，发送mq异常，或者部分数据更新成功了，后面更新数据异常。重试时数据已经一致，不会触发下发价格。
        // 解决方案（采用第2种方案）：1. 不判断是否变化，全部下发价格 2. 判断下发状态，下发状态如果是已下发，则不下发，未下发则再次下发
        return publishPriceList;
    }

    @Override
    public List<PriceResultsBO> saveOrUpdateAndGetPublishPrice(PriceSourceEnum sourceEnum, List<PriceResultsBO> newList, List<PriceResultsBO> oldList) {
        // 筛选更新还是新增
        List<PriceResultsBO> toInsertList = new ArrayList<>();
        List<PriceResultsUpdateBO> toUpdatePriceSegmentList = new ArrayList<>();
        List<PriceResultsUpdateBO> toUpdateSegmentList = new ArrayList<>();
        List<PriceResultsBO> publishPriceList = getAddUpdateTypeAndPublishPrice(newList, oldList, toInsertList, toUpdatePriceSegmentList, toUpdateSegmentList);
        // 持久化数据
        if (CollectionUtils.isNotEmpty(toInsertList)) {
            priceAdjustResultsService.saveBatch(toInsertList, sourceEnum);
        }
        if (CollectionUtils.isNotEmpty(toUpdatePriceSegmentList)) {
            priceAdjustResultsService.updatePriceSegment(toUpdatePriceSegmentList, sourceEnum);
        }
        if (CollectionUtils.isNotEmpty(toUpdateSegmentList)) {
            priceAdjustResultsService.updatePriceSegmentOnly(toUpdateSegmentList, sourceEnum);
        }

        return publishPriceList;
    }

    public List<PriceResultsBO> getUpdateTypeAndPublishPrice(PriceResultsBO newItem, PriceResultsBO existingItem, List<PriceResultsUpdateBO> toUpdatePriceSegmentList, List<PriceResultsUpdateBO> toUpdateSegmentList) {
        List<PriceResultsBO> publishPriceList = new ArrayList<>();
        // 数据库中存在，比较是否有变化
        boolean segmentEqual = PriceCompareUtils.arePriceTimelineListsEqual(existingItem.getPriceResultSegment(), newItem.getPriceResultSegment());
        boolean priceEqual = PriceCompareUtils.priceEqual(existingItem, newItem);
        if (!priceEqual) {
            // 近期价格有变化，价格代肯定有变化
            newItem.setId(existingItem.getId());
            PriceResultsUpdateBO updateBO = new PriceResultsUpdateBO();
            BeanUtils.copyProperties(newItem, updateBO);
            updateBO.setOldPrice(new PriceBO(existingItem.getPrice(), existingItem.getStartTime(), existingItem.getEndTime()
                    , existingItem.getNextPrice(), existingItem.getNextStartTime(), existingItem.getNextEndTime()));
            // 处理价格段合并逻辑
            List<PriceTimelineSegmentBO> mergedSegments = mergePriceSegments(existingItem.getPriceResultSegment(), newItem.getPriceResultSegment());
            updateBO.setPriceResultSegment(mergedSegments);

            // 设置当前价格和下一段价格信息
            updateBO.updatePriceInfo();

            toUpdatePriceSegmentList.add(updateBO);
            publishPriceList.add(newItem);
        } else if (!segmentEqual) {
            // 近期价格没变化只是价格代变化，只更新价格代，不下发价格
            if (!PushStatusEnum.SUC.equals(existingItem.getPushPosStatus())) {
                // 避免修改成功，下发失败情况，重试时需要下发
                publishPriceList.add(newItem);
            }

            PriceResultsUpdateBO updateBO = new PriceResultsUpdateBO();
            BeanUtils.copyProperties(newItem, updateBO);
            updateBO.setId(existingItem.getId());
            // 处理价格段合并逻辑
            List<PriceTimelineSegmentBO> mergedSegments = mergePriceSegments(existingItem.getPriceResultSegment(), newItem.getPriceResultSegment());
            updateBO.setPriceResultSegment(mergedSegments);

//             设置当前价格和下一段价格信息
//            updateBO.updatePriceInfo();
            toUpdateSegmentList.add(updateBO);
            ExLogger.logger().info("计算后的数据近期价格无变化，不下发：db：{}，new：{}", JSON.toJSONString(existingItem), JSON.toJSONString(newItem));
        } else {
            if (!PushStatusEnum.SUC.equals(existingItem.getPushPosStatus())) {
                // 避免修改成功，下发失败情况，重试时需要下发
                publishPriceList.add(newItem);
            }
            ExLogger.logger().info("计算后的数据近期价格、价格代均无变化，不更新、不下发：db：{}，new：{}", JSON.toJSONString(existingItem), JSON.toJSONString(newItem));
        }
        return publishPriceList;
    }

    @Override
    public List<PriceResultsBO> getAddUpdateTypeAndPublishPrice(List<PriceResultsBO> newItemList, List<PriceResultsBO> existingItemList, List<PriceResultsBO> toInsertList, List<PriceResultsUpdateBO> toUpdatePriceSegmentList, List<PriceResultsUpdateBO> toUpdateSegmentList) {
        List<PriceResultsBO> publishPriceList = new ArrayList<>();

        Map<String, PriceResultsBO> dbMap = existingItemList.stream()
                .collect(Collectors.toMap(
                        PriceResultsBO::buildUniqueKey,
                        item -> item,
                        (v1, v2) -> v1
                ));

        for (PriceResultsBO newItem : newItemList) {
            PriceResultsBO existingItem = dbMap.get(newItem.buildUniqueKey());

            if (existingItem == null) {
                // 数据库中没有，新增
                toInsertList.add(newItem);
                publishPriceList.add(newItem);
            } else {
                publishPriceList.addAll(getUpdateTypeAndPublishPrice(newItem, existingItem, toUpdatePriceSegmentList, toUpdateSegmentList));
            }
        }

        return publishPriceList;
    }

    @Override
    public List<PriceResultsBO> calculatePriceResult(PriceTypeEnum priceTypeEnum, String companyCode, String storeCode, List<String> erpCodeList) {
        if (StringUtils.isEmpty(storeCode) || CollectionUtils.isEmpty(erpCodeList)) {
            ExLogger.logger().warn("计算价格，参数为空，storeCode: {}, erpCodes: {}", storeCode, erpCodeList);
            throw new YxtBizException("计算价格，参数为空");
        }

        // 查询门店商品所有调价单价格明细
        List<PriceResultsBO> result = new ArrayList<>();
        List<List<String>> erpCodePartition = ListUtils.partition(erpCodeList, 100);
        erpCodePartition.forEach(erpCodes -> {
            // 查询主库
            List<PriceAdjustFormItemDetailBO> priceAdjustFormItemDetailList = priceAdjustFormItemDetailService.listEnableByStoreCodeErpCodes(priceTypeEnum, companyCode, storeCode, erpCodes, true);
            Set<String> goodsUniCodeSet = priceAdjustFormItemDetailList.stream().map(m -> m.getErpCode() + "_" + m.getPriceType().getValue()).collect(Collectors.toSet());
            // 处理查询不到的商品数据 todo qs
            List<String> filterErpCodeList;
            if (priceTypeEnum != null) {
                filterErpCodeList = erpCodes.stream().map(m -> m + "_" + priceTypeEnum.getValue()).collect(Collectors.toList());
            } else {
                filterErpCodeList = new ArrayList<>();
                for (PriceTypeEnum value : PriceTypeEnum.values()) {
                    filterErpCodeList.addAll(erpCodes.stream().map(m -> m + "_" + value.getValue()).collect(Collectors.toList()));
                }
            }
            Set<String> notItemDetailList = filterErpCodeList.stream().filter(erpCodeUni -> !goodsUniCodeSet.contains(erpCodeUni)).collect(Collectors.toSet());
            notItemDetailList.forEach(erpCodeUni -> {
                String[] split = erpCodeUni.split("_");
                if (split.length != 2) {
                    throw new YxtBizException("数据错误erpCodeUni" + erpCodeUni);
                }
                PriceResultsBO bo = new PriceResultsBO();
                bo.setErpCode(split[0]);
                bo.setStoreCode(storeCode);
                bo.setPriceType(PriceTypeEnum.valueOf(split[1]));
                // 获取门店缓存
                StoreDTO store = allStoreInfoCache.getStoreCacheOrRemote(storeCode);
                if (store == null) {
                    throw new YxtBizException("门店不存在" + priceTypeEnum);
                }
                bo.setStoreId(store.getId());
                bo.setCompanyCode(store.getBranchCode());
                bo.setPriceGroupCode(store.getPricegroupIdNumber());

                // 计算价格代
                bo.setPriceResultSegment(Collections.emptyList());

                result.add(bo);
            });
            result.addAll(calculatePriceResult(priceAdjustFormItemDetailList));
        });

        return result;
    }

    @Override
    public List<PriceResultsBO> calculatePriceResult(PriceTypeEnum priceTypeEnum, Collection<String> storeCodes, String erpCode) {
        if (StringUtils.isEmpty(erpCode) || CollectionUtils.isEmpty(storeCodes)) {
            ExLogger.logger().warn("计算价格，参数为空，storeCodes: {}, erpCode: {}", storeCodes, erpCode);
            throw new YxtBizException("计算价格，参数为空");
        }

        List<PriceResultsBO> result = new ArrayList<>();
        // 查询门店信息
        List<StoreDTO> storeDTOList = allStoreInfoCache.listByCodes(storeCodes);
        List<String> haveStoreCodeList = storeDTOList.stream().filter(f -> StringUtils.isNotEmpty(f.getBranchCode())).map(StoreDTO::getStCode).collect(Collectors.toList());
        List<String> notFoundStoreCodeList = storeCodes.stream().filter(f -> !haveStoreCodeList.contains(f)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notFoundStoreCodeList)) {
            throw new YxtBizException("门店无门店信息：" + JSON.toJSONString(notFoundStoreCodeList));
        }
        // 按照分公司分组，并且将门店编码按照分表hash算法排序，每次分批查询尽可能少查表
        Map<String, List<StoreDTO>> groupedAndSortedByCompany = storeDTOList.stream()
                .collect(Collectors.groupingBy(
                        StoreDTO::getBranchCode,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> {
                                    list.sort(Comparator.comparingInt(
                                            item -> priceAdjustFormItemDetailService.getTableHashIndex(item.getStCode())
                                    ));
                                    return list;
                                }
                        )
                ));
        groupedAndSortedByCompany.forEach((companyCode, storeDTOs) -> {
            List<List<StoreDTO>> storeDTOPartition = ListUtils.partition(storeDTOs, 500);
            storeDTOPartition.forEach(partitionStoreDTOs -> {
                Set<String> queryStoreCodeSet = partitionStoreDTOs.stream().map(StoreDTO::getStCode).collect(Collectors.toSet());
                // 查询主库
                List<PriceAdjustFormItemDetailBO> priceAdjustFormItemDetailList = priceAdjustFormItemDetailService.listEnableByStoreCodesErpCode(priceTypeEnum, companyCode, queryStoreCodeSet, erpCode, true);
                Set<String> existStoreCodeUniCodeSet = priceAdjustFormItemDetailList.stream().map(m -> m.getStoreCode() + "_" + m.getPriceType().getValue()).collect(Collectors.toSet());
                List<String> filterStoreCodeList;
                if (priceTypeEnum != null) {
                    filterStoreCodeList = queryStoreCodeSet.stream().map(m -> m + "_" + priceTypeEnum.getValue()).collect(Collectors.toList());
                } else {
                    filterStoreCodeList = new ArrayList<>();
                    for (PriceTypeEnum value : PriceTypeEnum.values()) {
                        filterStoreCodeList.addAll(queryStoreCodeSet.stream().map(m -> m + "_" + value.getValue()).collect(Collectors.toList()));
                    }
                }
                // 处理查询不到的门店的数据 todo qs
                Set<String> notItemDetailList = filterStoreCodeList.stream().filter(storeCodeUni -> !existStoreCodeUniCodeSet.contains(storeCodeUni)).collect(Collectors.toSet());
                notItemDetailList.forEach(storeCodeUni -> {
                    String[] split = storeCodeUni.split("_");
                    if (split.length != 2) {
                        throw new YxtBizException("数据错误storeCodeUni" + storeCodeUni);
                    }
                    String storeCode = split[0];
                    PriceResultsBO bo = new PriceResultsBO();
                    bo.setErpCode(erpCode);
                    bo.setStoreCode(storeCode);
                    bo.setPriceType(PriceTypeEnum.valueOf(split[1]));
                    // 获取门店缓存
                    StoreDTO store = allStoreInfoCache.getStoreCacheOrRemote(storeCode);
                    if (store == null) {
                        throw new YxtBizException("门店不存在" + storeCode);
                    }
                    bo.setStoreId(store.getId());
                    bo.setCompanyCode(store.getBranchCode());
                    bo.setPriceGroupCode(store.getPricegroupIdNumber());

                    // 计算价格代
                    bo.setPriceResultSegment(Collections.emptyList());

                    result.add(bo);
                });
                result.addAll(calculatePriceResult(priceAdjustFormItemDetailList));
            });
        });
        return result;
    }

    @Override
    public List<PriceResultsBO> calculatePriceResult(List<PriceAdjustFormItemDetailBO> priceDetailList) {
        return priceDetailList.stream()
                // 第1层：按价格类型分组
                .collect(Collectors.groupingBy(PriceAdjustFormItemDetailBO::getPriceType))
                .entrySet().stream()
                .flatMap(priceTypeEntry -> {
                    PriceTypeEnum priceType = priceTypeEntry.getKey();
                    Map<String, List<PriceAdjustFormItemDetailBO>> storeErpGroup = priceTypeEntry.getValue().stream()
                            // 第2层：按门店+商品分组
                            .collect(Collectors.groupingBy(b -> b.getStoreCode() + b.getErpCode()));

                    return storeErpGroup.values().stream().map(detailList -> {
                        PriceAdjustFormItemDetailBO first = detailList.get(0); // 提取代表元素

                        PriceResultsBO bo = new PriceResultsBO();
                        bo.setErpCode(first.getErpCode());
                        bo.setGoodsName(first.getGoodsName());
                        bo.setStoreCode(first.getStoreCode());
                        bo.setPriceType(priceType);
                        // 获取门店缓存
                        StoreDTO store = allStoreInfoCache.getStoreCacheOrRemote(first.getStoreCode());
                        if (store == null) {
                            throw new YxtBizException("门店不存在" + first.getStoreCode());
                        }
                        bo.setStoreId(store.getId());
                        bo.setCompanyCode(store.getBranchCode());
                        bo.setPriceGroupCode(store.getPricegroupIdNumber());
                        // 设置需要更新首次计算时间的id
                        bo.setUpdateFirstCalculateTimeIdList(detailList.stream()
                                .filter(f -> f.getFirstCalculateTime() == null)
                                .map(PriceAdjustFormItemDetailBO::getId).distinct().collect(Collectors.toList()));

                        // 计算价格代
                        List<PriceTimelineSegmentBO> segments = calculatePriceSegmentSingle(detailList);
                        if (CollectionUtils.isNotEmpty(segments)) {
                            bo.setPrice(segments.get(0).getPrice());
                            bo.setStartTime(segments.get(0).getStart());
                            bo.setEndTime(segments.get(0).getEnd());
                            bo.setFormNo(segments.get(0).getFormNo());
                            bo.setOrgType(segments.get(0).getOrgType());

                            if (segments.size() > 1) {
                                bo.setNextPrice(segments.get(1).getPrice());
                                bo.setNextStartTime(segments.get(1).getStart());
                                bo.setNextEndTime(segments.get(1).getEnd());
                            }

                            bo.setPriceResultSegment(segments);
                        } else {
                            bo.setPriceResultSegment(Collections.emptyList());
                        }

                        return bo;
                    });
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<PriceTimelineSegmentBO> calculatePriceSegmentSingle(List<PriceAdjustFormItemDetailBO> priceDetailList) {
        // 优先级按调价维度以此类推，门店 → 价格组 → 公司
        // 同一调价维度时优先取最后审批的单据
        List<CalculatePriceEvent> events = new ArrayList<>();

        for (PriceAdjustFormItemDetailBO item : priceDetailList) {
            if (!CancelStatusEnum.NOT_CANCELED.equals(item.getEnableStatus())) continue;
            LocalDate approvalDate = item.getAporovalTime().toLocalDate();
            if (approvalDate == null) {
                approvalDate = item.getStartTime();
            }
            if (approvalDate.isAfter(item.getEndTime())) {
                continue;
            }
            // 审核日期大于生效开始日期，生效日期使用审核日期
            LocalDate startTime = item.getStartTime();
            if (approvalDate.isAfter(startTime)) {
                startTime = approvalDate;
            }
            events.add(new CalculatePriceEvent(startTime, true, item));
            events.add(new CalculatePriceEvent(item.getEndTime().plusDays(1), false, item));
        }

        // 时间升序排序，开始事件排前
        events.sort(Comparator
                .comparing(CalculatePriceEvent::getDate)
                .thenComparing(e -> !e.isStart()));

        // 优先级比较器：门店 > 价格组 > 分公司，审批时间倒序、调价单创建时间倒序
        TreeSet<PriceAdjustFormItemDetailBO> activeSet = new TreeSet<>(
                Comparator
                        .comparingInt((PriceAdjustFormItemDetailBO o) -> o.getOrgType().getPriority())
                        .thenComparing(PriceAdjustFormItemDetailBO::getAporovalTime, Comparator.reverseOrder())
                        .thenComparing(PriceAdjustFormItemDetailBO::getFormCreateTime, Comparator.reverseOrder())
                        .thenComparing(PriceAdjustFormItemDetailBO::getFormItemNo) // 保证一致性
        );

        List<PriceTimelineSegmentBO> segments = new ArrayList<>();
        LocalDate prevDate = null;

        for (CalculatePriceEvent event : events) {
            LocalDate currentDate = event.getDate();

            if (prevDate != null && prevDate.isBefore(currentDate) && !activeSet.isEmpty()) {
                PriceAdjustFormItemDetailBO main = activeSet.first();
                Set<String> allFormNos = activeSet.stream()
                        .map(PriceAdjustFormItemDetailBO::getFormItemNo)
                        .collect(Collectors.toSet());

                segments.add(new PriceTimelineSegmentBO(
                        prevDate,
                        currentDate.minusDays(1),
                        main.getPrice(),
                        main.getFormNo(),
                        main.getFormItemNo(),
                        main.getOrgType(),
                        allFormNos
                ));
            }

            if (event.isStart()) {
                activeSet.add(event.getSource());
            } else {
                activeSet.remove(event.getSource());
            }

            prevDate = currentDate;
        }

        // 合并返回
        return mergeTimelineSegments(segments);
    }

    /**
     * 合并相同价格的时间
     *
     * @param rawSegments 计算好的每段时间
     * @return 合并的时间
     */
    private List<PriceTimelineSegmentBO> mergeTimelineSegments(List<PriceTimelineSegmentBO> rawSegments) {
        if (rawSegments.isEmpty()) return Collections.emptyList();

        List<PriceTimelineSegmentBO> result = new ArrayList<>();
        PriceTimelineSegmentBO current = rawSegments.get(0);

        for (int i = 1; i < rawSegments.size(); i++) {
            PriceTimelineSegmentBO next = rawSegments.get(i);

            boolean isAdjacent = current.getEnd().plusDays(1).equals(next.getStart());
            boolean samePrice = current.getPrice().compareTo(next.getPrice()) == 0;
            boolean sameFormNo = current.getFormNo().equals(next.getFormNo());

            // 时间段连续，且价格、条价单相同，合并为一段
            if (isAdjacent && samePrice && sameFormNo) {
                // 合并时间段 + 单号集合
//                if (CollectionUtils.isEmpty(current.getMergeList())) {
//                    current.setMergeList(new ArrayList<>());
//                    current.getMergeList().add(BeanUtil.copyProperties(current, PriceTimelineSegmentBO.PriceTimeSegment.class));
//                }
//                current.getMergeList().add(BeanUtil.copyProperties(next, PriceTimelineSegmentBO.PriceTimeSegment.class));
                current.setEnd(next.getEnd());
                current.getAllFormItemNos().addAll(next.getAllFormItemNos());
            } else {
                result.add(current);
                current = next;
            }
        }

        result.add(current); // 添加最后一段
        return result;
    }

    public static void main(String[] args) {
        List<PriceAdjustFormItemDetailBO> priceDetailList = new ArrayList<>();
        PriceAdjustFormItemDetailBO b0 = new PriceAdjustFormItemDetailBO();
        PriceAdjustFormItemDetailBO b1 = new PriceAdjustFormItemDetailBO();
        PriceAdjustFormItemDetailBO b2 = new PriceAdjustFormItemDetailBO();
        PriceAdjustFormItemDetailBO b3 = new PriceAdjustFormItemDetailBO();
        priceDetailList.add(b0);
        priceDetailList.add(b1);
        priceDetailList.add(b2);
        priceDetailList.add(b3);

        b0.setFormItemNo("000");
        b0.setEnableStatus(CancelStatusEnum.NOT_CANCELED);
        b0.setPriceType(PriceTypeEnum.RETAIL);
        b0.setPrice(new BigDecimal("10"));
        b0.setOrgType(DimensionEnum.PRICE_GROUP);
        b0.setStartTime(LocalDate.of(2025, 5, 22));
        b0.setEndTime(LocalDate.of(2025, 6, 25));
        b0.setAporovalTime(LocalDateTime.of(LocalDate.of(2025, 6, 25), LocalTime.of(22, 11, 11)));

        b1.setFormItemNo("111");
        b1.setEnableStatus(CancelStatusEnum.NOT_CANCELED);
        b1.setPriceType(PriceTypeEnum.RETAIL);
        b1.setPrice(new BigDecimal("20"));
        b1.setOrgType(DimensionEnum.STORE);
        b1.setStartTime(LocalDate.of(2025, 5, 24));
        b1.setEndTime(LocalDate.of(2025, 5, 28));
        b1.setAporovalTime(LocalDateTime.of(LocalDate.of(2025, 6, 25), LocalTime.of(22, 11, 11)));


        b2.setFormItemNo("222");
        b2.setEnableStatus(CancelStatusEnum.NOT_CANCELED);
        b2.setPriceType(PriceTypeEnum.RETAIL);
        b2.setPrice(new BigDecimal("30"));
        b2.setOrgType(DimensionEnum.COMPANY);
        b2.setStartTime(LocalDate.of(2025, 5, 27));
        b2.setEndTime(LocalDate.of(2025, 8, 5));
        b2.setAporovalTime(LocalDateTime.of(LocalDate.of(2025, 6, 25), LocalTime.of(22, 11, 11)));

        b3.setFormItemNo("333");
        b3.setEnableStatus(CancelStatusEnum.NOT_CANCELED);
        b3.setPriceType(PriceTypeEnum.RETAIL);
        b3.setPrice(new BigDecimal("20"));
        b3.setOrgType(DimensionEnum.STORE);
        b3.setStartTime(LocalDate.of(2025, 5, 27));
        b3.setEndTime(LocalDate.of(2025, 7, 5));
        b3.setAporovalTime(LocalDateTime.of(LocalDate.of(2025, 6, 25), LocalTime.of(22, 11, 12)));

//        List<PriceTimelineSegmentBO> re = calculate(priceDetailList);
//        List<PriceTimelineSegmentBO> re1 = mergeTimelineSegments(re);
//        System.out.println(re);
    }

    /**
     * 合并价格段：保留历史价格段，合并新的价格段
     *
     * @param existingSegments 数据库中已存在的价格段
     * @param newSegments      新计算出的价格段
     * @return 合并后的价格段列表
     */
    public List<PriceTimelineSegmentBO> mergePriceSegments(List<PriceTimelineSegmentBO> existingSegments, List<PriceTimelineSegmentBO> newSegments) {
        LocalDate currentDate = LocalDate.now();
        List<PriceTimelineSegmentBO> result = new ArrayList<>();

        if (CollectionUtils.isEmpty(existingSegments)) {
            return newSegments != null ? new ArrayList<>(newSegments) : new ArrayList<>();
        }

        boolean newSegmentsContainToday;
        if (CollectionUtils.isEmpty(newSegments)) {
            newSegmentsContainToday = true;
        } else {
            newSegmentsContainToday = newSegments.stream()
                    .anyMatch(segment -> !currentDate.isBefore(segment.getStart()) && !currentDate.isAfter(segment.getEnd()));
        }

        // 历史价格保留到的日期
        LocalDate truncatedDate;
        if (newSegmentsContainToday) {
            // 新价格段包含当天，历史价格段保留到昨天
            truncatedDate = currentDate.minusDays(1);
        } else {
            // 新价格段不包含当天，历史价格段保留到当天（包含当天）
            truncatedDate = currentDate;
        }
        for (PriceTimelineSegmentBO segment : existingSegments) {
            if (segment.getEnd().isBefore(truncatedDate) || segment.getEnd().equals(truncatedDate)) {
                // 结束时间在截止日期或之前的段，直接保留
                result.add(segment);
            } else if (segment.getStart().isBefore(truncatedDate) || segment.getStart().equals(truncatedDate)) {
                // 跨越当天的段，截断到截止日期
                PriceTimelineSegmentBO truncatedSegment = new PriceTimelineSegmentBO(
                        segment.getStart(),
                        truncatedDate,
                        segment.getPrice(),
                        segment.getFormNo(),
                        segment.getFormItemNo(),
                        segment.getOrgType(),
                        segment.getAllFormItemNos()
                );
                result.add(truncatedSegment);
            }
            // 完全在当天之后的段不保留，因为会被新价格段替换
        }

        if (CollectionUtils.isEmpty(newSegments)) {
            return result;
        }
        // 2. 添加新的价格段
        List<PriceTimelineSegmentBO> futureNewSegments = newSegments.stream()
                .filter(segment -> !segment.getEnd().isBefore(currentDate))
                .map(segment -> {
                    // 如果新价格段开始时间早于当前日期，调整为当前日期
                    if (segment.getStart().isBefore(currentDate)) {
                        return new PriceTimelineSegmentBO(
                                currentDate,
                                segment.getEnd(),
                                segment.getPrice(),
                                segment.getFormNo(),
                                segment.getFormItemNo(),
                                segment.getOrgType(),
                                segment.getAllFormItemNos()
                        );
                    }
                    return segment;
                })
                .collect(Collectors.toList());

        // 3. 检查是否可以合并最后一个历史段和第一个新段
        if (!result.isEmpty() && !futureNewSegments.isEmpty()) {
            PriceTimelineSegmentBO lastHistorySegment = result.get(result.size() - 1);
            PriceTimelineSegmentBO firstNewSegment = futureNewSegments.get(0);

            // 检查是否可以合并：时间连续、价格相同、调价单号相同
            boolean isAdjacent = lastHistorySegment.getEnd().plusDays(1).equals(firstNewSegment.getStart());
            boolean samePrice = lastHistorySegment.getPrice().compareTo(firstNewSegment.getPrice()) == 0;
            boolean sameFormNo = lastHistorySegment.getFormNo().equals(firstNewSegment.getFormNo());

            if (isAdjacent && samePrice && sameFormNo) {
                // 合并：扩展历史段的结束时间，合并调价单号集合
                lastHistorySegment.setEnd(firstNewSegment.getEnd());
                lastHistorySegment.getAllFormItemNos().addAll(firstNewSegment.getAllFormItemNos());

                // 移除第一个新段，因为已经合并了
                futureNewSegments.remove(0);

                ExLogger.logger().debug("合并价格段成功：历史段结束时间={}, 新段开始时间={}, 价格={}, 调价单号={}",
                        lastHistorySegment.getEnd(), firstNewSegment.getStart(), lastHistorySegment.getPrice(), lastHistorySegment.getFormNo());
            }
        }

        // 4. 添加剩余的新价格段
        result.addAll(futureNewSegments);

        // 5. 按时间排序并返回
        result.sort(Comparator.comparing(PriceTimelineSegmentBO::getStart));

        ExLogger.logger().debug("价格段合并完成：历史段数量={}, 新段数量={}, 合并后总数量={}, 新段包含当天={}",
                existingSegments.size(), newSegments.size(), result.size(), newSegmentsContainToday);

        return result;
    }

    @AllArgsConstructor
    @Getter
    public static class CalculatePriceEvent {
        private LocalDate date;
        private boolean isStart;
        private PriceAdjustFormItemDetailBO source;
    }

    /**
     * 处理任务失败数据并完成任务
     *
     * @param taskId 任务ID
     */
    private void processTaskFailDataAndFinish(Long taskId) {
        // 处理失败数据
        String failFileUrl = processFailDataFromRedis(taskId);
        // 完成任务
        batchTaskExtService.finishTask(taskId, failFileUrl);
        ExLogger.logger().info("任务完成，任务ID: {}, 失败文件: {}", taskId, failFileUrl);
    }

    /**
     * 从Redis中处理失败数据 - 优化版本：分批处理，避免内存溢出
     *
     * @param taskId 任务id
     * @return 失败文件URL
     */
    private String processFailDataFromRedis(Long taskId) {
        if (taskId == null) {
            return null;
        }

        String redisKey = String.format(RedisConstant.CALCULATION_PRICE_FAIL_KEY_FORMAT, taskId);

        try {
            // 先检查Redis中是否有数据
            Long totalCount = redisUtil.hashSize(redisKey);
            if (totalCount == null || totalCount == 0) {
                ExLogger.logger().info("任务： {} 没有失败数据", taskId);
                return null;
            }

            ExLogger.logger().info("任务： {} 开始分批处理失败数据，总计 {} 条", taskId, totalCount);

            // 生成文件名
            String fileName = String.format("yxt-lot-price/price_calculation_fail/%s_fail_%s.xlsx",
                    taskId, System.currentTimeMillis());

            // 使用分批流式处理，每批1000条数据
            String finalFileName = generateFailDataExcelBatch(taskId, redisKey, fileName);

            // 删除Redis中的失败数据
            redisUtil.delete(redisKey);

            ExLogger.logger().info("任务： {} 失败数据处理完成，文件：{}", taskId, finalFileName);
            return finalFileName;

        } catch (Exception e) {
            ExLogger.logger().error("处理任务 {} 失败数据异常", taskId, e);
            return null;
        }
    }

    /**
     * 分批生成失败数据Excel文件并上传到OSS - 优化版本
     * 使用流式处理，避免内存溢出
     *
     * @param taskId    任务id
     * @param redisKey  Redis键
     * @param fileName  文件名
     * @return 文件URL
     */
    private String generateFailDataExcelBatch(Long taskId, String redisKey, String fileName) {
        try {
            // 使用临时文件来避免内存溢出
            java.io.File tempFile = java.io.File.createTempFile("fail_data_" + taskId, ".xlsx");

            try (com.alibaba.excel.ExcelWriter excelWriter = EasyExcelFactory.write(tempFile, CalculationPriceFailExcelDTO.class).build()) {
                com.alibaba.excel.write.metadata.WriteSheet writeSheet = EasyExcelFactory.writerSheet("调价计算失败结果").build();

                // 分批处理数据，每批1000条
                final int batchSize = 1000;
                final int[] totalProcessed = {0};

                ExLogger.logger().info("开始分批处理Redis数据，批次大小: {}", batchSize);

                // 使用RedisUtil的分批查询方法
                redisUtil.hashScanBatchSimple(redisKey, batchSize, CalculationPriceFailExcelDTO.class,
                    (batchData, batchIndex) -> {
                        if (!batchData.isEmpty()) {
                            // 立即写入Excel，不在内存中积累
                            excelWriter.write(batchData, writeSheet);
                            totalProcessed[0] += batchData.size();

                            ExLogger.logger().debug("已处理批次 {}, 本批数据量: {}, 累计处理: {}",
                                batchIndex, batchData.size(), totalProcessed[0]);
                        }
                    });

                ExLogger.logger().info("Excel写入完成，总计处理数据: {} 条", totalProcessed[0]);
            }

            // 上传到OSS
            try (java.io.FileInputStream fileInputStream = new java.io.FileInputStream(tempFile)) {
                ossFileService.upload(fileInputStream, fileName);
                ExLogger.logger().info("成功上传失败数据文件到OSS: {}, 总数据量: {}", fileName, tempFile.length());
            }

            // 删除临时文件
            if (!tempFile.delete()) {
                ExLogger.logger().warn("删除临时文件失败: {}", tempFile.getAbsolutePath());
            }

            return fileName;

        } catch (Exception e) {
            ExLogger.logger().error("分批生成失败数据Excel文件异常，任务: {}", taskId, e);
            return null;
        }
    }

    /**
//     * 生成失败数据Excel文件并上传到OSS - 保留原方法作为备用
//     *
//     * @param taskId       任务id
//     * @param failDataList 失败数据列表
//     * @return 文件URL
//     */
//    private String generateFailDataExcel(Long taskId, List<CalculationPriceFailExcelDTO> failDataList) {
//        try {
//            // 生成文件名
//            String fileName = String.format("yxt-lot-price/price_calculation_fail/%s_fail_%s.xlsx",
//                    taskId, System.currentTimeMillis());
//
//            // 使用EasyExcel生成Excel
//            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//            EasyExcelFactory.write(outputStream, CalculationPriceFailExcelDTO.class)
//                    .sheet("调价计算失败结果")
//                    .doWrite(failDataList);
//
//            // 上传到OSS
//            ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
//            ossFileService.upload(inputStream, fileName);
//
//            ExLogger.logger().info("成功生成并上传失败数据文件: {}, 数据量: {}", fileName, failDataList.size());
//            return fileName;
//
//        } catch (Exception e) {
//            ExLogger.logger().error("生成失败数据Excel文件异常，任务: {}", taskId, e);
//            return null;
//        }
//    }
}
