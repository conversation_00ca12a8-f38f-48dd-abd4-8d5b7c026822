package com.yxt.lotprice.service.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.yxt.lotprice.service.constant.RedisConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/6 18:32
 */
@Component
@Slf4j
public class RedisUtil {
    @Resource
    private RedisTemplate<String,  Object> redisStringTemplate;

    public String buildGroupExeKey(String adjustCode, String empCode) {
        return RedisConstant.ADJUST_FORM_GROUP_EXEC_KEY+":" + adjustCode + ":" + empCode;
    }
    public String buildItemTmpSaveExeKey(String adjustCode, String empCode, Integer currentNodeId) {
        return RedisConstant.ADJUST_FORM_ITEM_TEMP_SAVE_KEY+":" + adjustCode + ":" + empCode+":"+currentNodeId;
    }
    public String buildAuditKey(String adjustCode) {
        return RedisConstant.ADJUST_FORM_AUDIT_KEY+":" + adjustCode;
    }

    /**
     * 保存/更新单个 hash 数据
     */
    public void hashSet(String key, String hashKey, Object itemData) {
        redisStringTemplate.opsForHash().put(key, hashKey, serialize(itemData));
    }

    /**
     * 批量保存 Items
     */
    public void hashSetBatch(String key, Map<String, ?> items) {
        if (items == null || items.isEmpty()) return;

        Map<String, String> serializedItems = new HashMap<>();
        items.forEach((itemId, data) ->
                serializedItems.put(itemId, serialize(data))
        );

        redisStringTemplate.opsForHash().putAll(key, serializedItems);

    }
    /**
     * 设置过期时间（秒）
     */
    public void setExpire(String key, long timeoutSeconds,TimeUnit timeUnit) {
        redisStringTemplate.expire(key, timeoutSeconds, timeUnit);
    }
    /**
     * 获取单个 Item 数据
     */
    public <T> T hashGet(String key, String hashKey, Class<T> clazz) {
        Object o = redisStringTemplate.opsForHash().get(key, hashKey);
        return deserialize(o, clazz);
    }

    /**
     * 获取所有 Items
     */
    public <T> Map<String, T> hashGetAll(String key, Class<T> clazz) {
        Map<Object, Object> entries = redisStringTemplate.opsForHash().entries(key);

        Map<String, T> result = new HashMap<>();
        entries.forEach((hashKey, value) -> {
            result.put((String) hashKey, deserialize(value, clazz));
        });

        return result;
    }

    /**
     * 原子减
     * @param key redis key
     * @param delta 减少数量
     */
    public Long decrement(String key, long delta) {
        return redisStringTemplate.opsForValue().decrement(key, delta);
    }

    /**
     * 原子减1
     * @param key redis key
     */
    public Long decrement(String key) {
        return decrement(key, 1L);
    }

    // JSON 序列化/反序列化
    private String serialize(Object obj) {
        if (obj == null) return null;
        return JSON.toJSONString(obj);
    }

    private <T> T deserialize(Object data, Class<T> clazz) {
        if (ObjectUtil.isEmpty(data)) return null;
        return JSON.parseObject((String)data, clazz);
    }

    public boolean delete(String key){
        return Boolean.TRUE.equals(redisStringTemplate.delete(key));
    }

    public void setValue(String key, Object value) {
        redisStringTemplate.opsForValue().set(key, value);
    }
    public void setValue(String key, Object value, long time, TimeUnit timeUnit) {
        redisStringTemplate.opsForValue().set(key, value);
        setExpire(key, time, timeUnit);
    }

    public Object get(String key) {
        return redisStringTemplate.opsForValue().get( key);
    }

    /**
     * 检查Hash中是否存在指定的hashKey
     */
    public boolean hashHasKey(String key, String hashKey) {
        return redisStringTemplate.opsForHash().hasKey(key, hashKey);
    }

    /**
     * 删除Hash中的指定hashKey
     */
    public void hashDelete(String key, String hashKey) {
        redisStringTemplate.opsForHash().delete(key, hashKey);
    }

    /**
     * 设置key的过期时间
     */
    public void expire(String key, long timeout, TimeUnit timeUnit) {
        redisStringTemplate.expire(key, timeout, timeUnit);
    }

    /**
     * 检查key是否存在
     */
    public boolean hasKey(String key) {
        return Boolean.TRUE.equals(redisStringTemplate.hasKey(key));
    }

    /**
     * 设置字符串值
     */
    public void set(String key, String value, long timeout, TimeUnit timeUnit) {
        redisStringTemplate.opsForValue().set(key, value, timeout, timeUnit);
    }

    /**
     * 向指定Key的List尾部追加数据
     * @param key   List的Key（如 adjust:audit:FORM_001）
     * @param itemData 要存储的对象（自动序列化）
     */
    public void rPush(String key, Object itemData) {
        redisStringTemplate.opsForList().rightPush(key, serialize(itemData));
    }

    public void lPush(String key, Object itemData) {
        redisStringTemplate.opsForList().leftPush(key, serialize(itemData));
    }

    /**
     * 从List头部弹出并返回一个元素
     * @param key List的Key
     * @param clazz 目标对象类型
     * @return 反序列化后的对象
     */
    public <T> T lPop(String key, Class<T> clazz) {
        Object o = redisStringTemplate.opsForList().leftPop(key);
        return o != null ? deserialize(o,clazz) : null;
    }
    /**
     * 返回list的size
     * @param key List的size
     */
    public Long listSize(String key) {
        return redisStringTemplate.opsForList().size(key);
    }

    /**
     * 从List尾部弹出并返回一个元素
     */
    public <T> T rPop(String key, Class<T> clazz) {
        Object o = redisStringTemplate.opsForList().rightPop(key);
        return o != null ?deserialize(o,clazz) : null;
    }

    /**
     * 获取List全部元素
     * @param key   List的Key
     * @param clazz 目标对象类型
     * @return 对象列表
     */
    public <T> List<T> listGetAll(String key, Class<T> clazz) {
        List<Object> list = redisStringTemplate.opsForList().range(key, 0, -1);
        if (CollUtil.isNotEmpty(list)){
            return list.stream()
                    .map(bytes -> deserialize(bytes, clazz))
                    .collect(Collectors.toList());
        }
        return null;
    }

    public <T> List<T> listAllAndRemove(String key, Class<T> clazz) {
        List<Object> list = redisStringTemplate.opsForList().range(key, 0, -1);
        if (CollUtil.isNotEmpty(list)){
            delete(key);
            return list.stream()
                    .map(bytes -> deserialize(bytes, clazz))
                    .collect(Collectors.toList());
        }
        return null;
    }


    /***
     * 验证set元素是否存在 不存在则添加
     * @param key
     * @param value
     * @param timeout
     * @param timeUnit
     * @return
     */
    public Boolean checkSet(String key, String value, Long timeout, TimeUnit timeUnit) {
        Long addCount = redisStringTemplate.opsForSet().add(key, value);
        if (timeout!=null &&timeout > 0){
            redisStringTemplate.expire(key, timeout, timeUnit);
        }
        return addCount!=null && addCount>0;
    }

    /**
     * 获取Hash的总数量（用于预估处理进度）
     * @param key Redis key
     * @return Hash中的元素总数
     */
    public Long hashSize(String key) {
        return redisStringTemplate.opsForHash().size(key);
    }

    /**
     * 简化的分批查询方法，自动管理游标
     * @param key Redis key
     * @param batchSize 每批次查询的数量
     * @param clazz 目标对象类型
     * @param processor 批次处理器
     */
    public <T> void hashScanBatchSimple(String key, int batchSize, Class<T> clazz, BatchProcessor<T> processor) {
        redisStringTemplate.execute((RedisCallback<Void>) connection -> {
            byte[] keyBytes = key.getBytes();
            ScanOptions options = ScanOptions.scanOptions().count(batchSize).build();
            Cursor<Map.Entry<byte[], byte[]>> cursor = connection.hScan(keyBytes, options);

            List<T> batchData = new ArrayList<>();
            int totalProcessed = 0;
            int batchIndex = 0;

            try {
                while (cursor.hasNext()) {
                    Map.Entry<byte[], byte[]> entry = cursor.next();
                    String value = new String(entry.getValue());
                    T deserializedValue = deserialize(value, clazz);

                    batchData.add(deserializedValue);

                    // 当达到批次大小时，立即处理这批数据
                    if (batchData.size() >= batchSize) {
                        processor.process(new ArrayList<>(batchData), batchIndex++); // 传递副本
                        totalProcessed += batchData.size();

                        // 清理当前批次数据，释放内存
                        batchData.clear();

                        log.debug("已处理一批数据，批次大小: {}, 累计处理: {}", batchSize, totalProcessed);
                    }
                }

                // 处理最后一批数据
                if (!batchData.isEmpty()) {
                    processor.process(new ArrayList<>(batchData), batchIndex);
                    totalProcessed += batchData.size();
                    log.debug("处理最后一批数据，批次大小: {}, 总计处理: {}", batchData.size(), totalProcessed);
                }

                log.info("Redis Hash 分批处理完成，总计处理: {} 条数据", totalProcessed);

            } finally {
                try {
                    cursor.close();
                } catch (Exception e) {
                    log.warn("关闭Redis游标异常", e);
                }
            }

            return null;
        });
    }

    /**
     * 批次处理器接口
     */
    @FunctionalInterface
    public interface BatchProcessor<T> {
        void process(List<T> batchData, int batchIndex);
    }
}
