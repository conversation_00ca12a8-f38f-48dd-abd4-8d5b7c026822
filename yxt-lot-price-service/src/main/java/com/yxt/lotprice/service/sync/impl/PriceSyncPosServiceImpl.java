package com.yxt.lotprice.service.sync.impl;

import com.alibaba.fastjson.JSON;
import com.yxt.common.wechatrobot.util.WxRobotOkHttpUtils;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.lotprice.common.config.PriceRobotConfig;
import com.yxt.lotprice.common.util.LocalDateUtils;
import com.yxt.lotprice.common.util.PriceUtils;
import com.yxt.lotprice.service.cache.AllStoreInfoCache;
import com.yxt.lotprice.service.calculation.PriceAdjustResultsService;
import com.yxt.lotprice.service.manager.iface.HydeePosManager;
import com.yxt.lotprice.service.manager.sdk.dto.StoreDTO;
import com.yxt.lotprice.service.model.bo.PriceResultsBO;
import com.yxt.lotprice.service.model.dto.mq.rocketmq.StorePriceDTO;
import com.yxt.lotprice.service.model.dto.sync.hdpos.HdPosRespBaseDTO;
import com.yxt.lotprice.service.model.dto.sync.hdpos.PriceSyncDTO;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import com.yxt.lotprice.service.model.enums.PushStatusEnum;
import com.yxt.lotprice.service.model.enums.log.PriceSourceEnum;
import com.yxt.lotprice.service.model.enums.third.HdApiEnum;
import com.yxt.lotprice.service.sync.PriceSyncService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 同步pos
 * Since: 2025/06/13 11:10
 * Author: qs
 */
@Slf4j
@Service
public class PriceSyncPosServiceImpl implements PriceSyncService {

    @Resource
    private PriceAdjustResultsService priceAdjustResultsService;

    @Resource
    private HydeePosManager hydeePosManager;

    @Resource
    private AllStoreInfoCache allStoreInfoCache;

    @Override
    public void priceSync(StorePriceDTO dto) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        if (dto == null || StringUtils.isEmpty(dto.getStoreCode())) {
            log.warn("价格同步参数为空或门店编码为空, {}", dto);
            return;
        }

        List<PriceResultsBO> priceResults = null;
        try {
            log.info("开始同步门店价格，门店编码: {}", dto.getStoreCode());

            // 1. 查询门店商品的最新价格数据
            priceResults = queryLatestPriceData(dto);

            if (CollectionUtils.isEmpty(priceResults)) {
                log.info("门店 {} 没有找到价格数据，跳过同步", dto.getStoreCode());
                return;
            }

            // 2. 转换为同步DTO
            PriceSyncDTO syncDTO = convertToPriceSyncDTO(dto.getStoreCode(), priceResults);

            // 3. 调用HTTP接口同步价格
            String errMsg = syncPriceToPos(syncDTO);

            // 修改状态
            PushStatusEnum pushResult;
            if (StringUtils.isEmpty(errMsg)) {
                pushResult = PushStatusEnum.SUC;
            } else {
                pushResult = PushStatusEnum.FAIL;
            }
            priceAdjustResultsService.updatePushPosResult(pushResult, priceResults, dto.getPriceSource(), errMsg);

            stopWatch.stop();
            log.info("门店价格同步完成，门店编码: {}, 同步商品数量: {}， 耗时：{}ms", dto.getStoreCode(), syncDTO.getGoodsList().size(), stopWatch.getTotalTimeMillis());
        } catch (Exception e) {
            log.error("门店价格同步失败，门店编码: {}", dto.getStoreCode(), e);
            if (CollectionUtils.isNotEmpty(priceResults)) {
                priceAdjustResultsService.updatePushPosResult(PushStatusEnum.FAIL, priceResults, dto.getPriceSource(), StringUtils.left(e.getMessage(), 30));
            }
            throw new YxtBizException("价格同步到 POS 失败: " + e.getMessage());
        }
    }

    @Override
    public void priceSync(List<PriceResultsBO> priceResults) {
        if (CollectionUtils.isEmpty(priceResults)) {
            return;
        }
        List<List<PriceResultsBO>> partition = ListUtils.partition(priceResults, 1000);
        for (List<PriceResultsBO> priceResultsBOList : partition) {
            // 2. 转换为同步DTO
            PriceSyncDTO syncDTO = convertToPriceSyncDTO(priceResultsBOList.get(0).getStoreCode(), priceResultsBOList);

            // 3. 调用HTTP接口同步价格
            String errMsg = syncPriceToPos(syncDTO);
            // 修改状态
            PushStatusEnum pushResult;
            if (StringUtils.isEmpty(errMsg)) {
                pushResult = PushStatusEnum.SUC;
            } else {
                pushResult = PushStatusEnum.FAIL;
            }
            priceAdjustResultsService.updatePushPosResult(pushResult, priceResults, PriceSourceEnum.HAND, errMsg);

            log.info("门店价格同步完成，门店编码: {}, 同步商品数量: {}", syncDTO.getStoreCode(), syncDTO.getGoodsList().size());
        }
    }

    /**
     * 查询门店商品的最新价格数据
     *
     * @param dto 门店价格DTO
     * @return 价格结果列表
     */
    private List<PriceResultsBO> queryLatestPriceData(StorePriceDTO dto) {
        List<PriceResultsBO> allResults = new ArrayList<>();

        if (CollectionUtils.isEmpty(dto.getGoodsList())) {
            log.warn("门店 {} 的商品列表为空", dto.getStoreCode());
            return allResults;
        }

        // 按价格类型分组查询
        Map<String, List<String>> priceTypeToErpCodes = new HashMap<>();
        for (StorePriceDTO.GoodsPrice goods : dto.getGoodsList()) {
            if (StringUtils.isNotEmpty(goods.getErpCode()) && StringUtils.isNotEmpty(goods.getPriceType())) {
                priceTypeToErpCodes.computeIfAbsent(goods.getPriceType(), k -> new ArrayList<>())
                        .add(goods.getErpCode());
            }
        }

        // 分批查询每种价格类型的商品
        for (Map.Entry<String, List<String>> entry : priceTypeToErpCodes.entrySet()) {
            String priceType = entry.getKey();
            List<String> erpCodes = entry.getValue();

            try {
                PriceTypeEnum priceTypeEnum = PriceTypeEnum.valueOf(priceType);

                // 分批查询，避免一次查询过多数据
                int batchSize = 1000;
                for (int i = 0; i < erpCodes.size(); i += batchSize) {
                    int endIndex = Math.min(i + batchSize, erpCodes.size());
                    List<String> batchErpCodes = erpCodes.subList(i, endIndex);

                    List<PriceResultsBO> batchResults = priceAdjustResultsService
                            .listByStoreCodePriceTypeErpCodes(null, dto.getStoreCode(), priceTypeEnum, batchErpCodes, true);

                    if (CollectionUtils.isNotEmpty(batchResults)) {
                        allResults.addAll(batchResults);
                    }
                }

            } catch (IllegalArgumentException e) {
                log.warn("无效的价格类型: {}, 跳过查询", priceType);
            }
        }

        log.info("门店 {} 查询到价格数据 {} 条", dto.getStoreCode(), allResults.size());
        return allResults;
    }

    /**
     * 转换为价格同步DTO
     *
     * @param storeCode    门店编码
     * @param priceResults 价格结果列表
     * @return 价格同步DTO
     */
    private PriceSyncDTO convertToPriceSyncDTO(String storeCode, List<PriceResultsBO> priceResults) {
        PriceSyncDTO syncDTO = new PriceSyncDTO();
        StoreDTO store = allStoreInfoCache.getStore(storeCode);
        if (store == null || StringUtils.isEmpty(store.getBranchCode())) {
            throw new YxtBizException("门店或分公司编码不存在:" + storeCode);
        }
        syncDTO.setCompanyCode(store.getBranchCode());
        syncDTO.setStoreCode(storeCode);
        syncDTO.setTimestamp(String.valueOf(System.currentTimeMillis()));

        // 按商品编码和价格类型分组
        Map<String, List<PriceResultsBO>> groupedResults = priceResults.stream()
                .collect(Collectors.groupingBy(item -> item.getErpCode() + "_" + item.getPriceType().name()));

        List<PriceSyncDTO.GoodsPrice> goodsList = new ArrayList<>();

        for (Map.Entry<String, List<PriceResultsBO>> entry : groupedResults.entrySet()) {
            List<PriceResultsBO> items = entry.getValue();
            if (CollectionUtils.isEmpty(items)) {
                continue;
            }

            PriceResultsBO firstItem = items.get(0);
            PriceSyncDTO.GoodsPrice goodsPrice = new PriceSyncDTO.GoodsPrice();
            goodsPrice.setErpCode(firstItem.getErpCode());
            goodsPrice.setPriceType(firstItem.getPriceType().name());

            // 构建价格段列表
            List<PriceSyncDTO.GoodsPrice.Price> priceList = buildPriceList(firstItem);
            goodsPrice.setPriceList(priceList);

            goodsList.add(goodsPrice);
        }

        syncDTO.setGoodsList(goodsList);
        return syncDTO;
    }

    /**
     * 构建价格段列表
     *
     * @param priceResult 价格结果
     * @return 价格段列表
     */
    private List<PriceSyncDTO.GoodsPrice.Price> buildPriceList(PriceResultsBO priceResult) {
        List<PriceSyncDTO.GoodsPrice.Price> priceList = new ArrayList<>();

        // 如果没有价格段，使用当前价格和下一段价格
        if (priceResult.getPrice() != null) {
            PriceSyncDTO.GoodsPrice.Price currentPrice = new PriceSyncDTO.GoodsPrice.Price();
            currentPrice.setSegmentFlag("1");
            currentPrice.setPrice(PriceUtils.roundToPriceString(priceResult.getPrice()));
            if (priceResult.getStartTime() != null) {
                currentPrice.setStartTime(LocalDateUtils.localDate2String(priceResult.getStartTime()));
            }
            if (priceResult.getEndTime() != null) {
                currentPrice.setEndTime(LocalDateUtils.localDate2String(priceResult.getEndTime()));
            }
            priceList.add(currentPrice);
        }

        if (priceResult.getNextPrice() != null) {
            PriceSyncDTO.GoodsPrice.Price nextPrice = new PriceSyncDTO.GoodsPrice.Price();
            nextPrice.setSegmentFlag("2");
            nextPrice.setPrice(priceResult.getNextPrice().toString());
            if (priceResult.getNextStartTime() != null) {
                nextPrice.setStartTime(LocalDateUtils.localDate2String(priceResult.getNextStartTime()));
            }
            if (priceResult.getNextEndTime() != null) {
                nextPrice.setEndTime(LocalDateUtils.localDate2String(priceResult.getNextStartTime()));
            }
            priceList.add(nextPrice);
        }

        return priceList;
    }

    /**
     * 同步价格到POS系统
     *
     * @param syncDTO 价格同步DTO
     */
    private String syncPriceToPos(PriceSyncDTO syncDTO) {
        HdPosRespBaseDTO<String> respBaseDTO = hydeePosManager.execute(HdApiEnum.SYNC_PRICE, syncDTO.getCompanyCode(), syncDTO.getStoreCode(), JSON.toJSONString(syncDTO), String.class);
        if (!respBaseDTO.checkSuccess()) {
//            WxRobotOkHttpUtils.post(PriceRobotConfig.PRICE_ALERT, String.format("同步价格到POS系统 返回业务码失败：%s", JSON.toJSONString(respBaseDTO)));
            return respBaseDTO.getMsg();
        }
        return "";
    }
}
